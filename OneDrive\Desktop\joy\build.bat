@echo off
REM Build script for Security Platform Process Evasion Tools
REM Requires Visual Studio Build Tools or Visual Studio installed

setlocal enabledelayedexpansion

echo ========================================
echo Security Platform Evasion Tools Builder
echo ========================================
echo.

REM Check for Visual Studio Build Tools
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
        set "VSINSTALLDIR=%%i"
    )
)

if not defined VSINSTALLDIR (
    echo [ERROR] Visual Studio Build Tools not found
    echo Please install Visual Studio Build Tools or Visual Studio
    pause
    exit /b 1
)

REM Setup build environment
set "VCVARSALL=%VSINSTALLDIR%\VC\Auxiliary\Build\vcvarsall.bat"
if not exist "%VCVARSALL%" (
    echo [ERROR] vcvarsall.bat not found
    pause
    exit /b 1
)

echo [INFO] Setting up build environment...
call "%VCVARSALL%" x64 >nul 2>&1

REM Create output directory
set "OUTPUT_DIR=bin"
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

echo [INFO] Building evasion tools...
echo.

REM Build ProcessHider
echo [BUILD] ProcessHider.exe
cl /EHsc /O2 /MT ProcessHider.cpp /Fe:%OUTPUT_DIR%\ProcessHider.exe kernel32.lib user32.lib advapi32.lib ntdll.lib >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] ProcessHider.exe built successfully
) else (
    echo [ERROR] Failed to build ProcessHider.exe
)

REM Build MemoryLoader
echo [BUILD] MemoryLoader.exe
cl /EHsc /O2 /MT MemoryLoader.cpp /Fe:%OUTPUT_DIR%\MemoryLoader.exe kernel32.lib user32.lib >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] MemoryLoader.exe built successfully
) else (
    echo [ERROR] Failed to build MemoryLoader.exe
)

REM Build AntiAnalysis
echo [BUILD] AntiAnalysis.exe
cl /EHsc /O2 /MT AntiAnalysis.cpp /Fe:%OUTPUT_DIR%\AntiAnalysis.exe kernel32.lib user32.lib advapi32.lib ntdll.lib >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] AntiAnalysis.exe built successfully
) else (
    echo [ERROR] Failed to build AntiAnalysis.exe
)

REM Copy PowerShell and batch scripts
echo [COPY] Copying scripts...
copy ProcessEvasion.ps1 "%OUTPUT_DIR%\" >nul 2>&1
copy RunHidden.bat "%OUTPUT_DIR%\" >nul 2>&1

REM Copy original executable
if exist "JoyToKey.exe" (
    copy JoyToKey.exe "%OUTPUT_DIR%\" >nul 2>&1
    echo [COPY] JoyToKey.exe copied to output directory
)

REM Clean up build artifacts
del *.obj >nul 2>&1

echo.
echo [INFO] Build completed!
echo [INFO] Output directory: %OUTPUT_DIR%
echo.

REM Create encrypted version of JoyToKey
if exist "%OUTPUT_DIR%\MemoryLoader.exe" if exist "%OUTPUT_DIR%\JoyToKey.exe" (
    echo [INFO] Creating encrypted version of JoyToKey.exe...
    "%OUTPUT_DIR%\MemoryLoader.exe" encrypt "%OUTPUT_DIR%\JoyToKey.exe" "%OUTPUT_DIR%\JoyToKey.enc" SecurityTestKey2025
    if %errorlevel% equ 0 (
        echo [SUCCESS] Encrypted version created: JoyToKey.enc
    ) else (
        echo [WARN] Failed to create encrypted version
    )
)

echo.
echo Available tools:
echo - ProcessHider.exe    : Advanced process hiding and obfuscation
echo - MemoryLoader.exe    : Memory-based PE loader with encryption
echo - AntiAnalysis.exe    : Anti-debugging and VM detection
echo - ProcessEvasion.ps1  : PowerShell-based evasion techniques
echo - RunHidden.bat       : Easy-to-use batch interface
echo.
echo Usage examples:
echo   ProcessHider.exe JoyToKey.exe
echo   MemoryLoader.exe load JoyToKey.enc
echo   PowerShell -ExecutionPolicy Bypass -File ProcessEvasion.ps1 -TargetExecutable JoyToKey.exe
echo   RunHidden.bat
echo.
pause
