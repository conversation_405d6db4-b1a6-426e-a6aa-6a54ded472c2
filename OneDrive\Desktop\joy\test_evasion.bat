@echo off
REM Comprehensive test script for the Security Platform Process Evasion Toolkit
REM This script validates all components and provides testing scenarios

setlocal enabledelayedexpansion

echo ==========================================
echo Security Platform Evasion Toolkit Tester
echo ==========================================
echo.

REM Check if we're in the right directory
if not exist "JoyToKey.exe" (
    echo [ERROR] JoyToKey.exe not found in current directory
    echo Please run this script from the JoyToKey installation directory
    pause
    exit /b 1
)

REM Check if tools are built
set "BIN_DIR=bin"
if not exist "%BIN_DIR%" (
    echo [INFO] Tools not built yet. Building now...
    call build.bat
    if %errorlevel% neq 0 (
        echo [ERROR] Build failed
        pause
        exit /b 1
    )
)

echo [INFO] Starting comprehensive evasion testing...
echo.

REM Test 1: Anti-Analysis Module
echo ========================================
echo TEST 1: Anti-Analysis Detection
echo ========================================
if exist "%BIN_DIR%\AntiAnalysis.exe" (
    echo [TEST] Running anti-analysis checks...
    "%BIN_DIR%\AntiAnalysis.exe"
    echo.
    echo [INFO] Anti-analysis test completed
) else (
    echo [SKIP] AntiAnalysis.exe not found
)

echo.
pause

REM Test 2: Memory Loader Encryption
echo ========================================
echo TEST 2: Memory Loader Encryption
echo ========================================
if exist "%BIN_DIR%\MemoryLoader.exe" (
    echo [TEST] Testing PE encryption...
    "%BIN_DIR%\MemoryLoader.exe" encrypt "JoyToKey.exe" "%BIN_DIR%\test_encrypted.enc" "TestKey123"
    
    if exist "%BIN_DIR%\test_encrypted.enc" (
        echo [SUCCESS] Encryption test passed
        
        echo [TEST] Testing encrypted PE loading...
        echo [INFO] This will launch JoyToKey from encrypted memory...
        echo [INFO] Press Ctrl+C if you want to skip this test
        pause
        
        "%BIN_DIR%\MemoryLoader.exe" load "%BIN_DIR%\test_encrypted.enc" "TestKey123"
        
        REM Cleanup
        del "%BIN_DIR%\test_encrypted.enc" 2>nul
    ) else (
        echo [FAIL] Encryption test failed
    )
) else (
    echo [SKIP] MemoryLoader.exe not found
)

echo.
pause

REM Test 3: PowerShell Evasion
echo ========================================
echo TEST 3: PowerShell Evasion Script
echo ========================================
if exist "ProcessEvasion.ps1" (
    echo [TEST] Testing PowerShell evasion (basic mode)...
    echo [INFO] This will create an obfuscated copy of JoyToKey
    
    powershell.exe -ExecutionPolicy Bypass -Command "& '.\ProcessEvasion.ps1' -TargetExecutable 'JoyToKey.exe' -ObfuscatedName 'svchost.exe'"
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] PowerShell evasion test completed
    ) else (
        echo [WARN] PowerShell evasion test had issues (this may be normal)
    )
) else (
    echo [SKIP] ProcessEvasion.ps1 not found
)

echo.
pause

REM Test 4: Process Hider
echo ========================================
echo TEST 4: Process Hider Tool
echo ========================================
if exist "%BIN_DIR%\ProcessHider.exe" (
    echo [TEST] Testing process hiding and obfuscation...
    echo [INFO] This will launch JoyToKey with a random system process name
    echo [INFO] Check Task Manager to see the obfuscated process name
    echo [INFO] Press Enter to continue or Ctrl+C to skip
    pause
    
    "%BIN_DIR%\ProcessHider.exe" "JoyToKey.exe"
    
    if %errorlevel% equ 0 (
        echo [SUCCESS] Process hider test completed
    ) else (
        echo [WARN] Process hider test had issues
    )
) else (
    echo [SKIP] ProcessHider.exe not found
)

echo.
pause

REM Test 5: Batch Interface
echo ========================================
echo TEST 5: Batch Interface
echo ========================================
if exist "RunHidden.bat" (
    echo [TEST] Testing batch interface...
    echo [INFO] This will open the interactive menu
    echo [INFO] You can test different evasion techniques manually
    echo [INFO] Press Enter to continue or Ctrl+C to skip
    pause
    
    call RunHidden.bat
) else (
    echo [SKIP] RunHidden.bat not found
)

REM Test Results Summary
echo.
echo ========================================
echo TEST RESULTS SUMMARY
echo ========================================
echo.

echo Components tested:
if exist "%BIN_DIR%\AntiAnalysis.exe" (
    echo [✓] AntiAnalysis.exe - Anti-debugging and VM detection
) else (
    echo [✗] AntiAnalysis.exe - Not found
)

if exist "%BIN_DIR%\MemoryLoader.exe" (
    echo [✓] MemoryLoader.exe - Memory-based PE loading
) else (
    echo [✗] MemoryLoader.exe - Not found
)

if exist "%BIN_DIR%\ProcessHider.exe" (
    echo [✓] ProcessHider.exe - Process hiding and obfuscation
) else (
    echo [✗] ProcessHider.exe - Not found
)

if exist "ProcessEvasion.ps1" (
    echo [✓] ProcessEvasion.ps1 - PowerShell evasion techniques
) else (
    echo [✗] ProcessEvasion.ps1 - Not found
)

if exist "RunHidden.bat" (
    echo [✓] RunHidden.bat - Interactive batch interface
) else (
    echo [✗] RunHidden.bat - Not found
)

echo.
echo ========================================
echo SECURITY PLATFORM TESTING CHECKLIST
echo ========================================
echo.
echo Test your security platform for detection of:
echo.
echo Process-based detection:
echo [ ] Processes with randomized system names (svchost.exe, etc.)
echo [ ] Processes running from temporary directories
echo [ ] Multiple instances of the same process with different names
echo [ ] Processes with obfuscated or missing PE headers
echo.
echo File-based detection:
echo [ ] Temporary file creation and deletion
echo [ ] Files copied to system directories
echo [ ] Encrypted/obfuscated executables
echo [ ] Files with legitimate names but suspicious behavior
echo.
echo Memory-based detection:
echo [ ] In-memory PE loading without file backing
echo [ ] Memory allocation patterns typical of process hollowing
echo [ ] Encrypted payloads being decrypted in memory
echo [ ] API hooking and DLL injection techniques
echo.
echo Behavioral detection:
echo [ ] Anti-debugging technique usage
echo [ ] VM/sandbox detection attempts
echo [ ] Analysis tool detection
echo [ ] Unusual process creation patterns
echo [ ] JoyToKey-specific behaviors despite name obfuscation
echo.
echo Network-based detection:
echo [ ] Network communications from obfuscated processes
echo [ ] DNS queries or HTTP requests from hidden processes
echo.
echo ========================================
echo RECOMMENDATIONS FOR SECURITY PLATFORMS
echo ========================================
echo.
echo 1. Implement behavioral analysis beyond process names
echo 2. Monitor memory allocation patterns and API usage
echo 3. Track file operations in temporary directories
echo 4. Analyze process parent-child relationships
echo 5. Implement heuristic detection for evasion techniques
echo 6. Monitor for anti-analysis behavior patterns
echo 7. Use machine learning for anomaly detection
echo 8. Implement kernel-level monitoring for process hiding
echo.
echo Testing completed! Review the results above to evaluate
echo your security platform's detection capabilities.
echo.
pause
