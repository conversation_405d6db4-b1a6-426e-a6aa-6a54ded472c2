@echo off
REM Security Platform Process Evasion Batch Script
REM This script provides multiple methods to run JoyToKey with evasion techniques

setlocal enabledelayedexpansion

echo ========================================
echo Security Platform Process Evasion Tool
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges
) else (
    echo [WARN] Not running as administrator - some features may be limited
)

REM Set variables
set "ORIGINAL_EXE=JoyToKey.exe"
set "TEMP_DIR=%TEMP%\SecTest_%RANDOM%"
set "OBFUSCATED_NAMES=svchost.exe explorer.exe winlogon.exe csrss.exe lsass.exe services.exe dwm.exe audiodg.exe"

REM Check if original executable exists
if not exist "%ORIGINAL_EXE%" (
    echo [ERROR] Original executable not found: %ORIGINAL_EXE%
    pause
    exit /b 1
)

echo [INFO] Original executable found: %ORIGINAL_EXE%

REM Create temporary directory
mkdir "%TEMP_DIR%" 2>nul

REM Menu for evasion techniques
echo.
echo Select evasion technique:
echo 1. Simple name obfuscation
echo 2. PowerShell-based evasion (recommended)
echo 3. Copy to system directory with random name
echo 4. Run from memory (advanced)
echo 5. Multiple process spawning
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto simple_obfuscation
if "%choice%"=="2" goto powershell_evasion
if "%choice%"=="3" goto system_directory
if "%choice%"=="4" goto memory_execution
if "%choice%"=="5" goto multiple_processes
if "%choice%"=="6" goto cleanup_exit
goto invalid_choice

:simple_obfuscation
echo.
echo [INFO] Using simple name obfuscation...

REM Generate random name from list
set "count=0"
for %%a in (%OBFUSCATED_NAMES%) do (
    set /a count+=1
    set "name!count!=%%a"
)

set /a random_num=%RANDOM% %% %count% + 1
call set "RANDOM_NAME=%%name!random_num!%%"

set "OBFUSCATED_PATH=%TEMP_DIR%\%RANDOM_NAME%"

echo [INFO] Copying to: %OBFUSCATED_PATH%
copy "%ORIGINAL_EXE%" "%OBFUSCATED_PATH%" >nul

if exist "%OBFUSCATED_PATH%" (
    echo [SUCCESS] Obfuscated executable created
    echo [INFO] Starting process with name: %RANDOM_NAME%
    start "" "%OBFUSCATED_PATH%"
    echo [INFO] Process started. Check Task Manager for process named: %RANDOM_NAME%
) else (
    echo [ERROR] Failed to create obfuscated executable
)
goto wait_and_cleanup

:powershell_evasion
echo.
echo [INFO] Using PowerShell-based evasion...

if not exist "ProcessEvasion.ps1" (
    echo [ERROR] ProcessEvasion.ps1 not found
    goto wait_and_cleanup
)

echo [INFO] Launching PowerShell evasion script...
powershell.exe -ExecutionPolicy Bypass -File "ProcessEvasion.ps1" -TargetExecutable "%ORIGINAL_EXE%" -HideFromTaskManager -EncryptPayload -AntiDebug

goto wait_and_cleanup

:system_directory
echo.
echo [INFO] Copying to system directory with random name...

REM Generate random system-like name
set "SYSTEM_NAMES=dwm.exe audiodg.exe conhost.exe RuntimeBroker.exe SearchIndexer.exe WmiPrvSE.exe dllhost.exe taskhost.exe"
set "count=0"
for %%a in (%SYSTEM_NAMES%) do (
    set /a count+=1
    set "sysname!count!=%%a"
)

set /a random_num=%RANDOM% %% %count% + 1
call set "SYS_NAME=%%sysname!random_num!%%"

set "SYSTEM_PATH=%WINDIR%\System32\%SYS_NAME%"

echo [INFO] Attempting to copy to: %SYSTEM_PATH%
copy "%ORIGINAL_EXE%" "%SYSTEM_PATH%" 2>nul

if exist "%SYSTEM_PATH%" (
    echo [SUCCESS] Copied to system directory
    echo [INFO] Starting system process: %SYS_NAME%
    start "" "%SYSTEM_PATH%"
    echo [INFO] Process started from system directory
) else (
    echo [WARN] Failed to copy to system directory (requires admin rights)
    echo [INFO] Falling back to temp directory...
    set "FALLBACK_PATH=%TEMP_DIR%\%SYS_NAME%"
    copy "%ORIGINAL_EXE%" "!FALLBACK_PATH!" >nul
    start "" "!FALLBACK_PATH!"
    echo [INFO] Process started from temp directory with system name
)
goto wait_and_cleanup

:memory_execution
echo.
echo [INFO] Memory execution technique...
echo [WARN] This requires advanced techniques - using alternative method

REM Create a VBS script for stealthier execution
set "VBS_SCRIPT=%TEMP_DIR%\runner.vbs"
echo Set objShell = CreateObject("WScript.Shell") > "%VBS_SCRIPT%"
echo objShell.Run """%ORIGINAL_EXE%""", 0, False >> "%VBS_SCRIPT%"

echo [INFO] Running via VBScript for stealth...
cscript //nologo "%VBS_SCRIPT%"
echo [INFO] Process started via VBScript (hidden window)
goto wait_and_cleanup

:multiple_processes
echo.
echo [INFO] Multiple process spawning technique...

set "SPAWN_COUNT=3"
echo [INFO] Spawning %SPAWN_COUNT% obfuscated processes...

for /L %%i in (1,1,%SPAWN_COUNT%) do (
    set "count=0"
    for %%a in (%OBFUSCATED_NAMES%) do (
        set /a count+=1
        set "name!count!=%%a"
    )
    
    set /a random_num=!RANDOM! %% !count! + 1
    call set "PROC_NAME=%%name!random_num!%%"
    
    set "PROC_PATH=%TEMP_DIR%\!PROC_NAME!"
    copy "%ORIGINAL_EXE%" "!PROC_PATH!" >nul
    
    if exist "!PROC_PATH!" (
        echo [INFO] Starting process %%i: !PROC_NAME!
        start "" "!PROC_PATH!"
        timeout /t 2 /nobreak >nul
    )
)

echo [SUCCESS] Multiple obfuscated processes started
goto wait_and_cleanup

:invalid_choice
echo [ERROR] Invalid choice. Please select 1-6.
pause
goto cleanup_exit

:wait_and_cleanup
echo.
echo [INFO] Evasion technique applied successfully
echo [INFO] Check your security platform for detection results
echo.
echo Press any key to cleanup and exit...
pause >nul

REM Cleanup processes (optional)
echo [INFO] Cleaning up temporary files...
if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%" 2>nul
)

REM Remove system directory copy if it exists
if exist "%SYSTEM_PATH%" (
    del "%SYSTEM_PATH%" 2>nul
)

echo [INFO] Cleanup completed

:cleanup_exit
echo.
echo [INFO] Security Platform Process Evasion Test Completed
echo ========================================
pause
exit /b 0
