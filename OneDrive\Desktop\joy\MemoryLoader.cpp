#include <windows.h>
#include <iostream>
#include <vector>
#include <string>
#include <fstream>

// Memory-based PE loader for security testing
class MemoryPELoader {
private:
    std::vector<BYTE> peData;
    LPVOID imageBase;
    DWORD imageSize;
    
    // XOR encryption/decryption
    std::vector<BYTE> xorCrypt(const std::vector<BYTE>& data, const std::string& key) {
        std::vector<BYTE> result(data.size());
        for (size_t i = 0; i < data.size(); ++i) {
            result[i] = data[i] ^ key[i % key.length()];
        }
        return result;
    }
    
    // Resolve imports
    bool resolveImports(LPVOID imageBase) {
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((DWORD_PTR)imageBase + dosHeader->e_lfanew);
        
        if (ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].Size == 0) {
            return true; // No imports to resolve
        }
        
        PIMAGE_IMPORT_DESCRIPTOR importDesc = (PIMAGE_IMPORT_DESCRIPTOR)((DWORD_PTR)imageBase + 
            ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].VirtualAddress);
        
        while (importDesc->Name != 0) {
            char* dllName = (char*)((DWORD_PTR)imageBase + importDesc->Name);
            HMODULE hModule = LoadLibraryA(dllName);
            
            if (!hModule) {
                return false;
            }
            
            PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)((DWORD_PTR)imageBase + importDesc->FirstThunk);
            PIMAGE_THUNK_DATA origThunk = (PIMAGE_THUNK_DATA)((DWORD_PTR)imageBase + importDesc->OriginalFirstThunk);
            
            while (thunk->u1.AddressOfData != 0) {
                FARPROC funcAddress;
                
                if (IMAGE_SNAP_BY_ORDINAL(origThunk->u1.Ordinal)) {
                    funcAddress = GetProcAddress(hModule, (LPCSTR)IMAGE_ORDINAL(origThunk->u1.Ordinal));
                } else {
                    PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)((DWORD_PTR)imageBase + origThunk->u1.AddressOfData);
                    funcAddress = GetProcAddress(hModule, importByName->Name);
                }
                
                if (!funcAddress) {
                    return false;
                }
                
                thunk->u1.Function = (DWORD_PTR)funcAddress;
                thunk++;
                origThunk++;
            }
            
            importDesc++;
        }
        
        return true;
    }
    
    // Apply relocations
    bool applyRelocations(LPVOID imageBase, DWORD_PTR delta) {
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((DWORD_PTR)imageBase + dosHeader->e_lfanew);
        
        if (ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC].Size == 0) {
            return true; // No relocations needed
        }
        
        PIMAGE_BASE_RELOCATION relocation = (PIMAGE_BASE_RELOCATION)((DWORD_PTR)imageBase + 
            ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC].VirtualAddress);
        
        while (relocation->VirtualAddress != 0) {
            DWORD relocCount = (relocation->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / sizeof(WORD);
            PWORD relocData = (PWORD)((DWORD_PTR)relocation + sizeof(IMAGE_BASE_RELOCATION));
            
            for (DWORD i = 0; i < relocCount; ++i) {
                WORD relocType = relocData[i] >> 12;
                WORD relocOffset = relocData[i] & 0xFFF;
                
                if (relocType == IMAGE_REL_BASED_HIGHLOW || relocType == IMAGE_REL_BASED_DIR64) {
                    PDWORD_PTR relocAddress = (PDWORD_PTR)((DWORD_PTR)imageBase + relocation->VirtualAddress + relocOffset);
                    *relocAddress += delta;
                }
            }
            
            relocation = (PIMAGE_BASE_RELOCATION)((DWORD_PTR)relocation + relocation->SizeOfBlock);
        }
        
        return true;
    }
    
    // Set memory protections
    bool setMemoryProtections(LPVOID imageBase) {
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((DWORD_PTR)imageBase + dosHeader->e_lfanew);
        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i) {
            DWORD protection = PAGE_NOACCESS;
            
            if (sectionHeader[i].Characteristics & IMAGE_SCN_MEM_EXECUTE) {
                if (sectionHeader[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
                    protection = PAGE_EXECUTE_READWRITE;
                } else {
                    protection = PAGE_EXECUTE_READ;
                }
            } else if (sectionHeader[i].Characteristics & IMAGE_SCN_MEM_WRITE) {
                protection = PAGE_READWRITE;
            } else if (sectionHeader[i].Characteristics & IMAGE_SCN_MEM_READ) {
                protection = PAGE_READONLY;
            }
            
            DWORD oldProtection;
            VirtualProtect((LPVOID)((DWORD_PTR)imageBase + sectionHeader[i].VirtualAddress),
                          sectionHeader[i].Misc.VirtualSize, protection, &oldProtection);
        }
        
        return true;
    }

public:
    // Load encrypted PE from file
    bool loadEncryptedPE(const std::string& filePath, const std::string& key) {
        std::ifstream file(filePath, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        // Read encrypted data
        file.seekg(0, std::ios::end);
        size_t fileSize = file.tellg();
        file.seekg(0, std::ios::beg);
        
        std::vector<BYTE> encryptedData(fileSize);
        file.read((char*)encryptedData.data(), fileSize);
        file.close();
        
        // Decrypt
        peData = xorCrypt(encryptedData, key);
        
        return loadPEFromMemory();
    }
    
    // Load PE directly from memory
    bool loadPEFromMemory() {
        if (peData.empty()) {
            return false;
        }
        
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)peData.data();
        if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
            return false;
        }
        
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)(peData.data() + dosHeader->e_lfanew);
        if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
            return false;
        }
        
        imageSize = ntHeaders->OptionalHeader.SizeOfImage;
        
        // Allocate memory for the image
        imageBase = VirtualAlloc(NULL, imageSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!imageBase) {
            return false;
        }
        
        // Copy headers
        memcpy(imageBase, peData.data(), ntHeaders->OptionalHeader.SizeOfHeaders);
        
        // Copy sections
        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        for (int i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i) {
            if (sectionHeader[i].SizeOfRawData > 0) {
                memcpy((LPVOID)((DWORD_PTR)imageBase + sectionHeader[i].VirtualAddress),
                       peData.data() + sectionHeader[i].PointerToRawData,
                       sectionHeader[i].SizeOfRawData);
            }
        }
        
        // Calculate relocation delta
        DWORD_PTR delta = (DWORD_PTR)imageBase - ntHeaders->OptionalHeader.ImageBase;
        
        // Apply relocations if needed
        if (delta != 0) {
            if (!applyRelocations(imageBase, delta)) {
                VirtualFree(imageBase, 0, MEM_RELEASE);
                return false;
            }
        }
        
        // Resolve imports
        if (!resolveImports(imageBase)) {
            VirtualFree(imageBase, 0, MEM_RELEASE);
            return false;
        }
        
        // Set proper memory protections
        setMemoryProtections(imageBase);
        
        return true;
    }
    
    // Execute the loaded PE
    bool execute() {
        if (!imageBase) {
            return false;
        }
        
        PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
        PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((DWORD_PTR)imageBase + dosHeader->e_lfanew);
        
        // Get entry point
        DWORD_PTR entryPoint = (DWORD_PTR)imageBase + ntHeaders->OptionalHeader.AddressOfEntryPoint;
        
        // Create thread to execute
        HANDLE hThread = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)entryPoint, NULL, 0, NULL);
        if (!hThread) {
            return false;
        }
        
        return true;
    }
    
    // Cleanup
    ~MemoryPELoader() {
        if (imageBase) {
            VirtualFree(imageBase, 0, MEM_RELEASE);
        }
    }
};

// Encryption utility
class PEEncryptor {
public:
    static bool encryptFile(const std::string& inputPath, const std::string& outputPath, const std::string& key) {
        std::ifstream input(inputPath, std::ios::binary);
        if (!input.is_open()) {
            return false;
        }
        
        // Read input file
        input.seekg(0, std::ios::end);
        size_t fileSize = input.tellg();
        input.seekg(0, std::ios::beg);
        
        std::vector<BYTE> data(fileSize);
        input.read((char*)data.data(), fileSize);
        input.close();
        
        // Encrypt
        std::vector<BYTE> encrypted(data.size());
        for (size_t i = 0; i < data.size(); ++i) {
            encrypted[i] = data[i] ^ key[i % key.length()];
        }
        
        // Write encrypted file
        std::ofstream output(outputPath, std::ios::binary);
        if (!output.is_open()) {
            return false;
        }
        
        output.write((char*)encrypted.data(), encrypted.size());
        output.close();
        
        return true;
    }
};

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cout << "Memory PE Loader for Security Testing" << std::endl;
        std::cout << "Usage:" << std::endl;
        std::cout << "  " << argv[0] << " encrypt <input.exe> <output.enc> [key]" << std::endl;
        std::cout << "  " << argv[0] << " load <encrypted.enc> [key]" << std::endl;
        return 1;
    }
    
    std::string mode = argv[1];
    std::string defaultKey = "SecurityTestKey2025";
    
    if (mode == "encrypt" && argc >= 4) {
        std::string inputPath = argv[2];
        std::string outputPath = argv[3];
        std::string key = (argc >= 5) ? argv[4] : defaultKey;
        
        std::cout << "Encrypting " << inputPath << " to " << outputPath << std::endl;
        
        if (PEEncryptor::encryptFile(inputPath, outputPath, key)) {
            std::cout << "Encryption successful!" << std::endl;
        } else {
            std::cout << "Encryption failed!" << std::endl;
            return 1;
        }
        
    } else if (mode == "load" && argc >= 3) {
        std::string encryptedPath = argv[2];
        std::string key = (argc >= 4) ? argv[3] : defaultKey;
        
        std::cout << "Loading encrypted PE from " << encryptedPath << std::endl;
        
        MemoryPELoader loader;
        if (loader.loadEncryptedPE(encryptedPath, key)) {
            std::cout << "PE loaded successfully into memory!" << std::endl;
            std::cout << "Executing..." << std::endl;
            
            if (loader.execute()) {
                std::cout << "Execution started!" << std::endl;
                std::cout << "Press Enter to exit..." << std::endl;
                std::cin.get();
            } else {
                std::cout << "Failed to execute PE!" << std::endl;
                return 1;
            }
        } else {
            std::cout << "Failed to load PE!" << std::endl;
            return 1;
        }
        
    } else {
        std::cout << "Invalid arguments!" << std::endl;
        return 1;
    }
    
    return 0;
}
